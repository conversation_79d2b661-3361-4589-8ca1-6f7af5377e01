import { defineAsyncComponent } from 'vue';
import Hawk<PERSON>oa<PERSON> from '~/common/components/atoms/hawk-loader.vue';

const BiDashboard = defineAsyncComponent({
  loader: () => import('~/bi/pages/bi-dashboard.vue'),
  loadingComponent: <PERSON><PERSON>oa<PERSON>,
});

const BiTablePreviewDemo = defineAsyncComponent({
  loader: () => import('~/bi/components/table-widgets/bi-table-preview-demo.vue'),
  loadingComponent: HawkLoader,
});

const routes = [
  {
    path: '/:asset_id?/bi-dashboard',
    name: 'bi-asset-dashboard',
    component: BiDashboard,
    meta: {
      title: 'BI Dashboard',
    },
  },
  {
    path: '/bi-table-preview-demo',
    name: 'bi-table-preview-demo',
    component: BiTablePreviewDemo,
    meta: {
      title: 'BI Table Preview Demo',
    },
  },
];
export default routes;
