<script setup>
import <PERSON> from 'papapar<PERSON>';
import { computed, onMounted, reactive } from 'vue';
import <PERSON><PERSON><PERSON>andson<PERSON> from './bi-handsontable.vue';

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  dataconfig: {
    type: Object,
    required: true,
  },
});

// ---------------------------------- State --------------------------------- //
const state = reactive({
  loading: false,
  error: null,
  csvData: [],
  columns: [],
  processedData: [],
});

// -------------------------------- Computed -------------------------------- //
const tableColumns = computed(() => {
  return state.columns.map(column => ({
    data: column,
    title: column,
    type: 'text',
    readOnly: true,
  }));
});

const columnConfig = computed(() => {
  // Apply column-based coloring based on dataconfig
  const config = {};

  return config;
});

// -------------------------------- Methods --------------------------------- //
async function fetchProgressHistoryData() {
  try {
    state.loading = true;
    state.error = null;

    // Fetch the CSV file from the public directory
    const csvPath = '/progress-history.csv';

    // For demo purposes, we'll simulate fetching from the actual file
    // In a real implementation, you might fetch from an API endpoint
    const response = await fetch(csvPath);

    if (!response.ok) {
      throw new Error(`Failed to fetch CSV: ${response.statusText}`);
    }

    const csvText = await response.text();

    // Parse CSV using Papa Parse
    const parseResult = Papa.parse(csvText, {
      header: true,
      skipEmptyLines: true,
      transformHeader: header => header.trim(),
    });

    if (parseResult.errors.length > 0) {
      console.warn('CSV parsing warnings:', parseResult.errors);
    }

    // Extract columns from the first row (headers)
    state.columns = parseResult.meta.fields || [];

    // Process the data
    state.csvData = parseResult.data;

    // Apply any data filtering/processing based on dataconfig
    state.processedData = processDataWithConfig(state.csvData);
  }
  catch (error) {
    console.error('Error fetching progress history data:', error);
    state.error = error.message;
  }
  finally {
    state.loading = false;
  }
}

function processDataWithConfig(data) {
  // Apply filtering, sorting, or other transformations based on dataconfig
  const processedData = [...data];

  return processedData;
}

// -------------------------------- Lifecycle ------------------------------- //
onMounted(() => {
  fetchProgressHistoryData();
});
</script>

<template>
  <div class="h-full w-full flex flex-col">
    <!-- Table Content -->
    <div class="flex-1 overflow-hidden">
      <div v-if="state.loading" class="h-full flex items-center justify-center">
        <div class="text-center">
          <HawkLoader class="w-8 h-8 mx-auto mb-4" />
          <p class="text-gray-600">
            Loading progress history data...
          </p>
        </div>
      </div>

      <BiHandsontable
        v-else
        
        :data="state.processedData"
        :columns="tableColumns"
        :column-config="columnConfig"
        :show-skeleton-loader="state.loading"
        height="100%"
        class="h-full"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
// Add any custom styles here
</style>
