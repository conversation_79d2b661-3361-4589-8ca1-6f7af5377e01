<script setup>
import { reactive } from 'vue';
import BiTablePreview from './bi-table-preview.vue';

// Demo state with different configurations
const state = reactive({
  selectedDemo: 'basic',
  demos: {
    basic: {
      id: 'demo-basic-001',
      dataconfig: {
        limit: 25,
      },
    },
    filtered: {
      id: 'demo-filtered-002',
      dataconfig: {
        filters: [
          {
            column: 'Category',
            operator: 'equals',
            value: 'Mechanical',
          },
        ],
        limit: 20,
      },
    },
    sorted: {
      id: 'demo-sorted-003',
      dataconfig: {
        sort: {
          column: 'Date',
          direction: 'desc',
        },
        limit: 30,
      },
    },
    complex: {
      id: 'demo-complex-004',
      dataconfig: {
        filters: [
          {
            column: 'Value',
            operator: 'greater_than',
            value: 5,
          },
          {
            column: 'Reset',
            operator: 'equals',
            value: 'False',
          },
        ],
        sort: {
          column: 'Date',
          direction: 'desc',
        },
        limit: 15,
      },
    },
  },
});

function selectDemo(demoKey) {
  state.selectedDemo = demoKey;
}
</script>

<template>
  <div class="h-screen w-full flex flex-col bg-gray-50">
    <!-- Demo Header -->
    <div class="bg-white border-b border-gray-200 p-4">
      <div class="max-w-7xl mx-auto">
        <h1 class="text-2xl font-bold text-gray-900 mb-4">
          BI Table Preview Demo
        </h1>
        <p class="text-gray-600 mb-4">
          This demo shows how the BiTablePreview component works with different configurations.
          It fetches data from progress-history.csv and applies filtering, sorting, and other transformations.
        </p>
        
        <!-- Demo Selection -->
        <div class="flex gap-2">
          <button
            v-for="(demo, key) in state.demos"
            :key="key"
            :class="[
              'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
              state.selectedDemo === key
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            ]"
            @click="selectDemo(key)"
          >
            {{ key.charAt(0).toUpperCase() + key.slice(1) }} Demo
          </button>
        </div>
      </div>
    </div>

    <!-- Demo Content -->
    <div class="flex-1 overflow-hidden">
      <div class="max-w-7xl mx-auto h-full p-4">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full overflow-hidden">
          <!-- Current Demo Info -->
          <div class="p-4 border-b border-gray-200 bg-gray-50">
            <h3 class="font-semibold text-gray-900 mb-2">
              Current Demo: {{ state.selectedDemo.charAt(0).toUpperCase() + state.selectedDemo.slice(1) }}
            </h3>
            <div class="text-sm text-gray-600">
              <div class="mb-1">
                <span class="font-medium">ID:</span> 
                {{ state.demos[state.selectedDemo].id }}
              </div>
              <div>
                <span class="font-medium">Configuration:</span>
                <pre class="inline ml-2 bg-gray-100 px-2 py-1 rounded text-xs">{{ JSON.stringify(state.demos[state.selectedDemo].dataconfig, null, 2) }}</pre>
              </div>
            </div>
          </div>

          <!-- Preview Component -->
          <div class="h-full overflow-hidden">
            <BiTablePreview
              :key="state.selectedDemo"
              :id="state.demos[state.selectedDemo].id"
              :dataconfig="state.demos[state.selectedDemo].dataconfig"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
// Demo-specific styles
</style>
